package main

import (
	"context"
	"fmt"
	"time"

	"memory-go/internal/config"
	"memory-go/internal/messaging"
	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

func main() {
	fmt.Println("=== Kafka 连接和消息发送诊断工具 ===")

	// 加载配置
	cfg, err := config.Load("configs/memory_analyzer.yaml")
	if err != nil {
		fmt.Printf("❌ 加载配置失败: %v\n", err)
		return
	}

	fmt.Printf("📋 Kafka 配置:\n")
	fmt.Printf("   服务器: %v\n", cfg.Kafka.BootstrapServers)
	fmt.Printf("   主题配置:\n")
	for key, topic := range cfg.Kafka.Topics {
		fmt.Printf("     %s: %s\n", key, topic)
	}

	// 创建日志器
	log := logger.New(cfg.Logging)

	// 创建消息管理器
	fmt.Println("\n🔌 创建 Kafka 连接...")
	messageManager, err := messaging.NewManager(&cfg.Kafka, log)
	if err != nil {
		fmt.Printf("❌ 创建消息管理器失败: %v\n", err)
		fmt.Println("\n可能的原因:")
		fmt.Println("1. Kafka 服务器不可达")
		fmt.Println("2. 网络连接问题")
		fmt.Println("3. Kafka 服务未启动")
		return
	}
	defer messageManager.Close()

	fmt.Println("✅ Kafka 连接创建成功")

	ctx := context.Background()
	flowID := fmt.Sprintf("diagnostic_%d", time.Now().Unix())

	// 测试 1: 发送进度消息
	fmt.Println("\n📤 测试 1: 发送进度消息...")
	progressMsg := &models.ProgressMessage{
		FlowID:    flowID,
		Plugin:    "diagnostic.test",
		Progress:  25.0,
		Status:    "running",
		Message:   "诊断测试进度消息",
		Timestamp: time.Now().Unix(),
	}

	err = messageManager.PublishProgress(ctx, progressMsg)
	if err != nil {
		fmt.Printf("❌ 发送进度消息失败: %v\n", err)
	} else {
		fmt.Printf("✅ 进度消息发送成功 (主题: %s)\n", cfg.Kafka.Topics["progress"])
	}

	// 等待一下
	time.Sleep(1 * time.Second)

	// 测试 2: 发送结果消息
	fmt.Println("\n📤 测试 2: 发送结果消息...")
	dataMsg := &models.DataMessage{
		FlowID:     flowID,
		Plugin:     "diagnostic.test",
		ResultPath: "/diagnostic/test_result.json",
		Status:     "completed",
		Timestamp:  time.Now().Unix(),
	}

	err = messageManager.PublishResult(ctx, dataMsg)
	if err != nil {
		fmt.Printf("❌ 发送结果消息失败: %v\n", err)
	} else {
		fmt.Printf("✅ 结果消息发送成功 (主题: %s)\n", cfg.Kafka.Topics["result"])
	}

	// 等待一下
	time.Sleep(1 * time.Second)

	// 测试 3: 发送错误消息
	fmt.Println("\n📤 测试 3: 发送错误消息...")
	err = messageManager.PublishError(ctx, flowID, "diagnostic.test", "这是一个诊断测试错误消息")
	if err != nil {
		fmt.Printf("❌ 发送错误消息失败: %v\n", err)
	} else {
		fmt.Printf("✅ 错误消息发送成功 (主题: %s)\n", cfg.Kafka.Topics["error"])
	}

	// 等待一下
	time.Sleep(1 * time.Second)

	// 测试 4: 发送任务状态消息
	fmt.Println("\n📤 测试 4: 发送任务状态消息...")
	task := &models.Task{
		ID:        "diagnostic_task_123",
		FlowID:    flowID,
		Status:    models.TaskStatusCompleted,
		Progress:  100.0,
		CreatedAt: time.Now().Add(-5 * time.Minute),
	}
	completedAt := time.Now()
	task.CompletedAt = &completedAt

	err = messageManager.PublishTaskStatus(ctx, task)
	if err != nil {
		fmt.Printf("❌ 发送任务状态消息失败: %v\n", err)
	} else {
		fmt.Printf("✅ 任务状态消息发送成功\n")
	}

	// 等待消息发送完成
	fmt.Println("\n⏳ 等待消息发送完成...")
	time.Sleep(3 * time.Second)

	fmt.Println("\n=== 诊断完成 ===")
	fmt.Printf("🔍 请检查 Kafka 主题 'mfp_result' 中是否有以下消息:\n")
	fmt.Printf("   - 进度消息 (message_type: progress, flow_id: %s)\n", flowID)
	fmt.Printf("   - 结果消息 (message_type: data, flow_id: %s)\n", flowID)
	fmt.Printf("   - 错误消息 (message_type: error, flow_id: %s)\n", flowID)
	fmt.Printf("   - 任务状态消息 (message_type: task_status, flow_id: %s)\n", flowID)

	fmt.Println("\n💡 如果仍然看不到消息，请检查:")
	fmt.Println("   1. Kafka 主题 'mfp_result' 是否存在")
	fmt.Println("   2. Kafka 消费者是否正确订阅了该主题")
	fmt.Println("   3. Kafka 服务器日志是否有错误信息")
	fmt.Println("   4. 网络连接是否稳定")
}
