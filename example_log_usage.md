# 内存分析日志系统使用示例

## 日志文件命名规则

每个内存分析请求都会生成一个独立的日志文件，文件名格式为：
```
{analysis_id}.log
```

例如：
- 分析ID: `vzvi0sMJz_JYlIMIaSJfq`
- 日志文件名: `vzvi0sMJz_JYlIMIaSJfq.log`

## 日志文件位置

日志文件存储在配置的临时目录下的 `analysis_logs` 子目录中：
```
{temp_dir}/analysis_logs/{analysis_id}.log
```

默认路径示例：
```
/tmp/memory-go/analysis_logs/vzvi0sMJz_JYlIMIaSJfq.log
```

## 日志内容示例

```
2024-01-15 10:30:15 [INFO] === Memory Analysis Started ===
2024-01-15 10:30:15 [INFO] Task ID: task_12345
2024-01-15 10:30:15 [INFO] Analysis ID: vzvi0sMJz_JYlIMIaSJfq
2024-01-15 10:30:15 [INFO] Start Time: 2024-01-15 10:30:15
2024-01-15 10:30:15 [INFO] =====================================
2024-01-15 10:30:15 [INFO] Starting memory analysis
2024-01-15 10:30:15 [INFO] Flow ID: flow_12345
2024-01-15 10:30:15 [INFO] Analysis ID: vzvi0sMJz_JYlIMIaSJfq
2024-01-15 10:30:15 [INFO] OS: windows
2024-01-15 10:30:15 [INFO] File Path: /path/to/memory.dmp
2024-01-15 10:30:15 [INFO] File Size: 1073741824 bytes
2024-01-15 10:30:16 [INFO] Step 1: Downloading memory dump file
2024-01-15 10:30:20 [INFO] Memory dump downloaded successfully: /tmp/memory_12345.dmp
2024-01-15 10:30:20 [INFO] Step 2: Executing standard plugins (3 plugins)
2024-01-15 10:30:20 [INFO] --- Starting plugin: pslist ---
2024-01-15 10:30:20 [INFO] Executing Volatility command: vol -f /tmp/memory_12345.dmp windows.pslist
2024-01-15 10:30:25 [INFO] Plugin pslist executed successfully, processing 45 records
2024-01-15 10:30:25 [INFO] Processed 45 records for plugin pslist
2024-01-15 10:30:25 [INFO] Uploading 1 files for plugin pslist
2024-01-15 10:30:26 [INFO] Uploaded file: /tmp/pslist_result.json -> s3://bucket/results/pslist_12345.json (size: 15234 bytes)
2024-01-15 10:30:26 [INFO] --- Plugin pslist completed in 6s, processed 45 records ---
2024-01-15 10:30:26 [INFO] Progress: 23.3% - Completed plugin: pslist
2024-01-15 10:30:26 [INFO] --- Starting plugin: netstat ---
2024-01-15 10:30:26 [INFO] Executing Volatility command: vol -f /tmp/memory_12345.dmp windows.netstat
2024-01-15 10:30:30 [INFO] Plugin netstat executed successfully, processing 12 records
2024-01-15 10:30:30 [INFO] Processed 12 records for plugin netstat
2024-01-15 10:30:30 [INFO] --- Plugin netstat completed in 4s, processed 12 records ---
2024-01-15 10:30:30 [INFO] Progress: 46.6% - Completed plugin: netstat
2024-01-15 10:30:30 [INFO] --- Starting plugin: filescan ---
2024-01-15 10:30:30 [INFO] Executing Volatility command: vol -f /tmp/memory_12345.dmp windows.filescan
2024-01-15 10:30:45 [INFO] Plugin filescan executed successfully, processing 1234 records
2024-01-15 10:30:45 [INFO] Processed 1234 records for plugin filescan
2024-01-15 10:30:45 [INFO] Uploading 1 files for plugin filescan
2024-01-15 10:30:46 [INFO] Uploaded file: /tmp/filescan_result.json -> s3://bucket/results/filescan_12345.json (size: 234567 bytes)
2024-01-15 10:30:46 [INFO] --- Plugin filescan completed in 16s, processed 1234 records ---
2024-01-15 10:30:46 [INFO] Progress: 70.0% - Completed plugin: filescan
2024-01-15 10:30:46 [INFO] Cleaned up temporary dump file
2024-01-15 10:30:46 [INFO] =====================================
2024-01-15 10:30:46 [INFO] === Memory Analysis Completed ===
2024-01-15 10:30:46 [INFO] Duration: 31s
2024-01-15 10:30:46 [INFO] Success: true
2024-01-15 10:30:46 [INFO] End Time: 2024-01-15 10:30:46
2024-01-15 10:30:46 [INFO] =====================================
```

## API 使用

### 获取分析日志
```bash
GET /memory_parse/log/{analysis_id}
```

示例请求：
```bash
curl -X GET "http://localhost:8080/memory_parse/log/vzvi0sMJz_JYlIMIaSJfq"
```

示例响应：
```json
{
  "code": 0,
  "message": "success",
  "log_content": "2024-01-15 10:30:15 [INFO] === Memory Analysis Started ===\n..."
}
```

### 错误响应
如果日志文件不存在：
```json
{
  "code": 404,
  "message": "log not found",
  "desc": "log file not found for analysis_id: vzvi0sMJz_JYlIMIaSJfq"
}
```

## 特性

1. **唯一标识**: 每个分析请求使用唯一的 analysis_id 作为日志文件名
2. **完整记录**: 记录从开始到结束的完整分析过程
3. **详细信息**: 包含每个插件的执行详情、耗时、处理记录数等
4. **错误跟踪**: 记录所有错误和警告信息
5. **进度跟踪**: 记录分析进度和各个阶段的完成情况
6. **文件操作**: 记录文件下载、上传等操作的详细信息
7. **易于查询**: 通过 analysis_id 可以直接获取对应的日志内容