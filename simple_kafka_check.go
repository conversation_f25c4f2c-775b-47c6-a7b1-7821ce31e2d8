package main

import (
	"fmt"
	"time"

	"github.com/confluentinc/confluent-kafka-go/v2/kafka"
)

func main() {
	fmt.Println("=== 简单 Kafka 连接测试 ===")

	// 直接创建 Kafka 生产者配置
	config := &kafka.ConfigMap{
		"bootstrap.servers":   "192.168.2.151:19092",
		"client.id":           "memory-go-test",
		"acks":                "1",
		"retries":             3,
		"linger.ms":           10,
		"compression.type":    "snappy",
		"metadata.max.age.ms": 30000,
		"socket.timeout.ms":   10000,
	}

	fmt.Printf("配置: %+v\n", config)

	// 创建生产者
	producer, err := kafka.NewProducer(config)
	if err != nil {
		fmt.Printf("❌ 创建生产者失败: %v\n", err)
		return
	}
	defer producer.Close()

	fmt.Println("✅ 生产者创建成功")

	// 发送测试消息
	topic := "mfp_result"
	message := &kafka.Message{
		TopicPartition: kafka.TopicPartition{
			Topic:     &topic,
			Partition: kafka.PartitionAny,
		},
		Value: []byte(`{"test": "message", "timestamp": "` + time.Now().Format(time.RFC3339) + `"}`),
		Key:   []byte("test-key"),
	}

	fmt.Printf("发送消息到主题: %s\n", topic)

	// 发送消息
	deliveryChan := make(chan kafka.Event, 1)
	err = producer.Produce(message, deliveryChan)
	if err != nil {
		fmt.Printf("❌ 发送消息失败: %v\n", err)
		return
	}

	// 等待投递结果
	select {
	case e := <-deliveryChan:
		if msg, ok := e.(*kafka.Message); ok {
			if msg.TopicPartition.Error != nil {
				fmt.Printf("❌ 消息投递失败: %v\n", msg.TopicPartition.Error)
			} else {
				fmt.Printf("✅ 消息投递成功: 主题=%s, 分区=%d, 偏移量=%d\n",
					*msg.TopicPartition.Topic,
					msg.TopicPartition.Partition,
					msg.TopicPartition.Offset)
			}
		}
	case <-time.After(30 * time.Second):
		fmt.Println("❌ 消息投递超时")
	}

	// 刷新所有待发送的消息
	producer.Flush(5000)

	fmt.Println("=== 测试完成 ===")
}
