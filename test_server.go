package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// 简单的测试脚本，用于验证服务器功能
func main() {
	baseURL := "http://localhost:8080"

	// 测试健康检查
	fmt.Println("Testing health endpoint...")
	resp, err := http.Get(baseURL + "/health")
	if err != nil {
		fmt.Printf("Health check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("Health response: %s\n", string(body))

	// 测试版本信息
	fmt.Println("\nTesting version endpoint...")
	resp, err = http.Get(baseURL + "/version")
	if err != nil {
		fmt.Printf("Version check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	fmt.Printf("Version response: %s\n", string(body))

	// 测试系统状态
	fmt.Println("\nTesting status endpoint...")
	resp, err = http.Get(baseURL + "/status")
	if err != nil {
		fmt.Printf("Status check failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	fmt.Printf("Status response: %s\n", string(body))

	// 测试内存分析请求
	fmt.Println("\nTesting memory parse endpoint...")
	testRequest := map[string]interface{}{
		"file_info": map[string]interface{}{
			"type":     "file",
			"sub_type": "memory_dump",
			"attribute": map[string]interface{}{
				"os": "Windows",
			},
			"oss": map[string]interface{}{
				"file":   "test_memory.bin",
				"bucket": "memory-dumps",
			},
		},
		"options": map[string]interface{}{
			"parse_items": map[string]interface{}{
				"windows.pslist.PsList": map[string]interface{}{},
			},
			"upload_option": map[string]interface{}{
				"bucket": "results",
			},
			"show_progress": true,
		},
		"extra": map[string]interface{}{
			"analysis_id": "test_analysis_123",
			"flow_id":     fmt.Sprintf("test_flow_%d", time.Now().Unix()),
		},
	}

	jsonData, _ := json.Marshal(testRequest)
	resp, err = http.Post(baseURL+"/memory_parse", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("Memory parse request failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	fmt.Printf("Memory parse response: %s\n", string(body))

	// 测试任务列表
	fmt.Println("\nTesting tasks endpoint...")
	resp, err = http.Get(baseURL + "/tasks")
	if err != nil {
		fmt.Printf("Tasks request failed: %v\n", err)
		return
	}
	defer resp.Body.Close()

	body, _ = io.ReadAll(resp.Body)
	fmt.Printf("Tasks response: %s\n", string(body))

	fmt.Println("\nAll tests completed!")
}
