package engine

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"memory-go/internal/config"
	"memory-go/internal/logging"
	"memory-go/internal/messaging"
	"memory-go/internal/models"
	"memory-go/internal/processor"
	"memory-go/internal/storage"
	"memory-go/internal/volatility"
	"memory-go/pkg/logger"
)

// AnalysisEngine 分析引擎核心
type AnalysisEngine struct {
	config          *config.Config
	logger          *logger.Logger
	activeTasks     map[string]*models.Task
	taskMutex       sync.RWMutex
	volatilityExec  *volatility.Executor
	resultProcessor *processor.ResultProcessor
	storageManager  *storage.DynamicManager
	messageManager  *messaging.Manager
	scheduler       *TaskScheduler
}

// New 创建新的分析引擎
func New(cfg *config.Config, log *logger.Logger) *AnalysisEngine {
	// 创建 Volatility 执行器
	volatilityExec := volatility.New(&cfg.Volatility, log)

	// 创建动态存储管理器
	dynamicStorageManager := storage.NewDynamicManager(log)

	// 创建消息管理器
	messageManager, err := messaging.NewManager(&cfg.Kafka, log)
	if err != nil {
		log.WithError(err).Error("Failed to create message manager")
		// 继续运行，但消息功能将不可用
	}

	// 创建任务调度器
	maxConcurrentTasks := cfg.Volatility.MaxWorkers
	if maxConcurrentTasks <= 0 {
		maxConcurrentTasks = 4 // 默认值
	}
	scheduler := NewTaskScheduler(maxConcurrentTasks, 100, log) // 队列大小为100

	// 初始化任务日志管理器 - 使用项目目录下的 logs 文件夹
	logDir := "logs"
	logging.InitTaskLoggerManager(logDir)

	return &AnalysisEngine{
		config:          cfg,
		logger:          log,
		activeTasks:     make(map[string]*models.Task),
		volatilityExec:  volatilityExec,
		resultProcessor: processor.New(log),
		storageManager:  dynamicStorageManager,
		messageManager:  messageManager,
		scheduler:       scheduler,
	}
}

// ProcessMemoryDump 处理内存转储分析请求
func (e *AnalysisEngine) ProcessMemoryDump(ctx context.Context, req *models.MemoryParseRequest) error {
	// 解析请求参数
	fileInfo, err := e.parseFileInfo(req.FileInfo)
	if err != nil {
		return fmt.Errorf("failed to parse file info: %w", err)
	}

	options, err := e.parseOptions(req.Options)
	if err != nil {
		return fmt.Errorf("failed to parse options: %w", err)
	}

	extra, err := e.parseExtra(req.Extra)
	if err != nil {
		return fmt.Errorf("failed to parse extra info: %w", err)
	}

	// 创建任务
	task := &models.Task{
		ID:        generateTaskID(),
		FlowID:    extra.FlowID,
		Status:    models.TaskStatusPending,
		Request:   req,
		CreatedAt: time.Now(),
		Progress:  0.0,
		Metadata:  make(map[string]interface{}),
	}

	// 创建任务专用日志器（在任务创建时就开始记录）
	taskLogger, err := logging.CreateTaskLogger(task.FlowID, extra.AnalysisID)
	if err != nil {
		e.logger.WithError(err).Error("Failed to create task logger")
		// 继续执行，但没有专用日志
	} else {
		// 记录请求接收
		taskLogger.LogInfo("=== Memory Analysis Request Received ===")
		taskLogger.LogInfo("Request received at: %s", time.Now().Format("2006-01-02 15:04:05"))
		taskLogger.LogInfo("Task ID: %s", task.ID)
		taskLogger.LogInfo("Flow ID: %s", task.FlowID)
		taskLogger.LogInfo("Analysis ID: %s", extra.AnalysisID)
		taskLogger.LogInfo("File Info: Bucket=%s, Key=%s, OS=%s", fileInfo.OSS.Bucket, fileInfo.OSS.File, fileInfo.Attribute.OS.Type)
		taskLogger.LogInfo("Parse Items: %d plugins requested", len(options.ParseItems))
		for pluginName := range options.ParseItems {
			taskLogger.LogInfo("  - Plugin: %s", pluginName)
		}
		if options.CustomParseItems != nil && len(options.CustomParseItems) > 0 {
			taskLogger.LogInfo("Custom Parse Items: %d plugins requested", len(options.CustomParseItems))
			for pluginName := range options.CustomParseItems {
				taskLogger.LogInfo("  - Custom Plugin: %s", pluginName)
			}
		}
		taskLogger.LogInfo("Show Progress: %t", options.ShowProgress)
		taskLogger.LogInfo("============================================")
	}

	// 注册任务
	e.registerTask(task)

	// 异步执行分析
	go func() {
		defer e.unregisterTask(task.FlowID)

		// 创建独立的上下文，不依赖于 HTTP 请求上下文
		analysisCtx := context.Background()

		// 可以添加超时控制
		if e.config.Volatility.Timeout > 0 {
			var cancel context.CancelFunc
			analysisCtx, cancel = context.WithTimeout(analysisCtx, time.Duration(e.config.Volatility.Timeout)*time.Second)
			defer cancel()
		}

		if err := e.executeAnalysis(analysisCtx, task, fileInfo, options, extra); err != nil {
			e.logger.WithError(err).WithField("flow_id", task.FlowID).Error("Analysis failed")
			task.Status = models.TaskStatusFailed
			task.Error = err.Error()

			// 发送错误通知
			if sendErr := e.sendErrorNotification(analysisCtx, task.FlowID, "analysis", err.Error()); sendErr != nil {
				e.logger.WithError(sendErr).WithField("flow_id", task.FlowID).Error("Failed to send error notification")
			}
		} else {
			task.Status = models.TaskStatusCompleted
		}

		completedAt := time.Now()
		task.CompletedAt = &completedAt
		task.Progress = 100.0

		// 发送任务完成通知
		if err := e.sendTaskStatusNotification(analysisCtx, task); err != nil {
			e.logger.WithError(err).WithField("flow_id", task.FlowID).Error("Failed to send task completion notification")
		}
	}()

	return nil
}

// CancelTask 取消任务
func (e *AnalysisEngine) CancelTask(flowID string) error {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()

	task, exists := e.activeTasks[flowID]
	if !exists {
		return fmt.Errorf("no running task found for flow_id %s", flowID)
	}

	// 检查任务是否已经完成或取消
	if task.Status == models.TaskStatusCompleted ||
		task.Status == models.TaskStatusFailed ||
		task.Status == models.TaskStatusCancelled {
		return fmt.Errorf("task %s is already in final state: %s", flowID, task.Status)
	}

	// 更新任务状态
	task.Status = models.TaskStatusCancelled
	cancelledAt := time.Now()
	task.CompletedAt = &cancelledAt
	task.Error = "Task cancelled by user request"

	// 终止相关的 Volatility 进程
	activeProcs := e.volatilityExec.ListActiveProcesses()
	killedProcesses := 0
	for _, proc := range activeProcs {
		// 如果进程 ID 包含 flow_id，则终止该进程
		if strings.Contains(proc.ID, flowID) {
			if err := e.volatilityExec.KillProcess(proc.ID); err != nil {
				e.logger.WithError(err).WithField("process_id", proc.ID).Warn("Failed to kill process")
			} else {
				killedProcesses++
			}
		}
	}

	// 发送取消通知
	if err := e.sendTaskStatusNotification(context.Background(), task); err != nil {
		e.logger.WithError(err).WithField("flow_id", flowID).Error("Failed to send task cancellation notification")
	}

	e.logger.WithFields(map[string]interface{}{
		"flow_id":          flowID,
		"killed_processes": killedProcesses,
	}).Info("Task cancelled successfully")

	return nil
}

// GetTaskStatus 获取任务状态
func (e *AnalysisEngine) GetTaskStatus(flowID string) (*models.Task, error) {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	task, exists := e.activeTasks[flowID]
	if !exists {
		return nil, fmt.Errorf("task not found for flow_id %s", flowID)
	}

	return task, nil
}

// ListActiveTasks 列出活跃任务
func (e *AnalysisEngine) ListActiveTasks() []*models.Task {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	tasks := make([]*models.Task, 0, len(e.activeTasks))
	for _, task := range e.activeTasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// executeAnalysis 执行分析流程
func (e *AnalysisEngine) executeAnalysis(ctx context.Context, task *models.Task, fileInfo *models.FileInfo, options *models.AnalysisOptions, extra *models.ExtraInfo) error {
	// 获取已创建的任务日志器
	taskLogger, _ := logging.GetTaskLogger(task.FlowID)

	// 确保在函数结束时关闭任务日志器
	defer func() {
		if taskLogger != nil {
			taskLogger.LogTaskComplete(time.Since(*task.StartedAt), task.Status == models.TaskStatusCompleted)
			logging.CloseTaskLogger(task.FlowID)
		}
	}()

	// 更新任务状态
	task.Status = models.TaskStatusRunning
	startedAt := time.Now()
	task.StartedAt = &startedAt

	// 记录到任务日志
	if taskLogger != nil {
		taskLogger.LogInfo("Starting memory analysis")
		taskLogger.LogInfo("Flow ID: %s", task.FlowID)
		taskLogger.LogInfo("Analysis ID: %s", extra.AnalysisID)
		taskLogger.LogInfo("OS: %s", fileInfo.Attribute.OS.Type)
		taskLogger.LogInfo("File Bucket: %s", fileInfo.OSS.Bucket)
		taskLogger.LogInfo("File Key: %s", fileInfo.OSS.File)
	}

	e.logger.WithFields(map[string]interface{}{
		"flow_id":     task.FlowID,
		"analysis_id": extra.AnalysisID,
		"os":          fileInfo.Attribute.OS.Type,
	}).Info("Starting memory analysis")

	// 1. 下载内存转储文件
	if taskLogger != nil {
		taskLogger.LogInfo("Step 1: Downloading memory dump file")
	}
	e.logger.WithField("flow_id", task.FlowID).Info("Downloading memory dump")
	dumpPath, err := e.downloadMemoryDump(ctx, fileInfo)
	if err != nil {
		if taskLogger != nil {
			taskLogger.LogError("Failed to download memory dump: %v", err)
		}
		return fmt.Errorf("failed to download memory dump: %w", err)
	}
	if taskLogger != nil {
		taskLogger.LogInfo("Memory dump downloaded successfully: %s", dumpPath)
	}
	defer func() {
		if err := os.Remove(dumpPath); err != nil {
			e.logger.WithError(err).Warn("Failed to cleanup dump file")
			if taskLogger != nil {
				taskLogger.LogWarn("Failed to cleanup dump file: %v", err)
			}
		} else if taskLogger != nil {
			taskLogger.LogInfo("Cleaned up temporary dump file")
		}
	}()

	task.Progress = 10.0

	// 2. 下载符号文件（如果需要）
	if options.Symbol.FilePath != "" {
		e.logger.WithField("flow_id", task.FlowID).Info("Downloading symbol files")
		if err := e.downloadSymbols(ctx, &options.Symbol); err != nil {
			e.logger.WithError(err).Warn("Failed to download symbols, continuing without them")
		}
	}

	task.Progress = 20.0

	// 3. 执行分析插件
	totalPlugins := len(options.ParseItems)
	if options.CustomParseItems != nil {
		totalPlugins += len(options.CustomParseItems)
	}

	progressPerPlugin := 70.0 / float64(totalPlugins) // 70% 用于插件执行

	// 执行标准插件
	if taskLogger != nil {
		taskLogger.LogInfo("Step 2: Executing standard plugins (%d plugins)", len(options.ParseItems))
	}

	for pluginName := range options.ParseItems {
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		if taskLogger != nil {
			taskLogger.LogPluginStart(pluginName)
		}

		e.logger.WithFields(map[string]interface{}{
			"task_id":   task.FlowID,
			"component": "volatility",
			"plugin":    pluginName,
		}).Info("Executing plugin")

		pluginStart := time.Now()
		if err := e.executePlugin(ctx, task, pluginName, dumpPath, fileInfo.Attribute.OS.Type, options); err != nil {
			e.logger.WithError(err).WithField("plugin", pluginName).Error("Plugin execution failed")
			if taskLogger != nil {
				taskLogger.LogPluginError(pluginName, err)
			}
			// 继续执行其他插件，不因单个插件失败而终止整个分析
		} else {
			pluginDuration := time.Since(pluginStart)
			if taskLogger != nil {
				taskLogger.LogPluginComplete(pluginName, pluginDuration, 0) // 记录数量待实现
			}
		}

		task.Progress += progressPerPlugin
		if taskLogger != nil {
			taskLogger.LogProgress(task.Progress, fmt.Sprintf("Completed plugin: %s", pluginName))
		}
	}

	// 执行自定义插件
	if options.CustomParseItems != nil {
		for pluginName := range options.CustomParseItems {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
			}

			e.logger.WithFields(map[string]interface{}{
				"flow_id": task.FlowID,
				"plugin":  pluginName,
			}).Info("Executing custom plugin")

			if err := e.executeCustomPlugin(ctx, task, pluginName, dumpPath, fileInfo.Attribute.OS.Type, options); err != nil {
				e.logger.WithError(err).WithField("plugin", pluginName).Error("Custom plugin execution failed")
			}

			task.Progress += progressPerPlugin
		}
	}

	task.Progress = 100.0

	e.logger.WithField("flow_id", task.FlowID).Info("Memory analysis completed successfully")
	return nil
}

// registerTask 注册任务
func (e *AnalysisEngine) registerTask(task *models.Task) {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	e.activeTasks[task.FlowID] = task
}

// unregisterTask 注销任务
func (e *AnalysisEngine) unregisterTask(flowID string) {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()
	delete(e.activeTasks, flowID)
}

// parseFileInfo 解析文件信息
func (e *AnalysisEngine) parseFileInfo(fileInfoMap map[string]interface{}) (*models.FileInfo, error) {
	fileInfo := &models.FileInfo{}

	if val, ok := fileInfoMap["type"].(string); ok {
		fileInfo.Type = val
	}

	if val, ok := fileInfoMap["sub_type"].(string); ok {
		fileInfo.SubType = val
	}

	if attrMap, ok := fileInfoMap["attribute"].(map[string]interface{}); ok {
		// 处理 os 字段，支持字符串或对象格式
		if os, ok := attrMap["os"].(string); ok {
			fileInfo.Attribute.OS.Type = os
		} else if osMap, ok := attrMap["os"].(map[string]interface{}); ok {
			if osType, ok := osMap["type"].(string); ok {
				fileInfo.Attribute.OS.Type = osType
			}
		}
	}

	if ossMap, ok := fileInfoMap["oss"].(map[string]interface{}); ok {
		if file, ok := ossMap["file"].(string); ok {
			fileInfo.OSS.File = file
		}
		if bucket, ok := ossMap["bucket"].(string); ok {
			fileInfo.OSS.Bucket = bucket
		}
	}

	// 验证必要字段
	if fileInfo.Type != "file" || fileInfo.SubType != "memory_dump" {
		return nil, fmt.Errorf("invalid file type: expected file/memory_dump, got %s/%s", fileInfo.Type, fileInfo.SubType)
	}

	if fileInfo.Attribute.OS.Type == "" {
		return nil, fmt.Errorf("missing OS attribute")
	}

	if fileInfo.OSS.File == "" || fileInfo.OSS.Bucket == "" {
		return nil, fmt.Errorf("missing OSS file or bucket information")
	}

	return fileInfo, nil
}

// parseOptions 解析分析选项
func (e *AnalysisEngine) parseOptions(optionsMap map[string]interface{}) (*models.AnalysisOptions, error) {
	options := &models.AnalysisOptions{}

	// 解析上传选项
	if val, ok := optionsMap["upload_option"].(map[string]interface{}); ok {
		uploadOption := models.UploadOption{}
		if typ, ok := val["type"].(string); ok {
			uploadOption.Type = typ
		}
		if bucket, ok := val["bucket"].(string); ok {
			uploadOption.Bucket = bucket
		}
		if path, ok := val["path"].(string); ok {
			uploadOption.Path = path
		}
		if ossMap, ok := val["oss"].(map[string]interface{}); ok {
			uploadOption.OSS = parseOSSConfig(ossMap)
		}
		options.UploadOption = uploadOption
	}

	// 解析符号选项
	if val, ok := optionsMap["symbol"].(map[string]interface{}); ok {
		symbolOption := models.SymbolOption{}
		if typ, ok := val["type"].(string); ok {
			symbolOption.Type = typ
		}
		if bucket, ok := val["bucket"].(string); ok {
			symbolOption.Bucket = bucket
		}
		if filePath, ok := val["file_path"].(string); ok {
			symbolOption.FilePath = filePath
		}
		if ossMap, ok := val["oss"].(map[string]interface{}); ok {
			symbolOption.OSS = parseOSSConfig(ossMap)
		}
		options.Symbol = symbolOption
	}

	// 解析 Kafka 选项
	if val, ok := optionsMap["kafka_option"].(map[string]interface{}); ok {
		kafkaOption := models.KafkaOption{}
		if topics, ok := val["topic"].([]interface{}); ok {
			for _, topic := range topics {
				if topicStr, ok := topic.(string); ok {
					kafkaOption.Topic = append(kafkaOption.Topic, topicStr)
				}
			}
		}
		options.KafkaOption = kafkaOption
	}

	if val, ok := optionsMap["show_progress"].(bool); ok {
		options.ShowProgress = val
	}

	if val, ok := optionsMap["parse_items"]; ok {
		options.ParseItems = val.(map[string]interface{})
	}

	if val, ok := optionsMap["custom_parse_items"]; ok {
		options.CustomParseItems = val.(map[string]interface{})
	}

	// 验证必要字段
	if options.ParseItems == nil || len(options.ParseItems) == 0 {
		return nil, fmt.Errorf("no parse items specified")
	}

	return options, nil
}

// parseExtra 解析额外信息
func (e *AnalysisEngine) parseExtra(extraMap map[string]interface{}) (*models.ExtraInfo, error) {
	extra := &models.ExtraInfo{}

	if val, ok := extraMap["analysis_id"].(string); ok {
		extra.AnalysisID = val
	}

	if val, ok := extraMap["flow_id"].(string); ok {
		extra.FlowID = val
	} else if val, ok := extraMap["analysis_id"].(string); ok {
		// 如果没有 flow_id，使用 analysis_id 作为 flow_id
		extra.FlowID = val
	}

	// 验证必要字段
	if extra.FlowID == "" {
		return nil, fmt.Errorf("missing flow_id or analysis_id")
	}

	return extra, nil
}

// 临时实现的占位符方法，后续任务中会实现具体逻辑

func (e *AnalysisEngine) downloadMemoryDump(ctx context.Context, fileInfo *models.FileInfo) (string, error) {
	if e.storageManager == nil {
		return "", fmt.Errorf("storage manager not available")
	}

	e.logger.WithFields(map[string]interface{}{
		"file":   fileInfo.OSS.File,
		"bucket": fileInfo.OSS.Bucket,
		"host":   fileInfo.DownloadOption.OSS.Host,
	}).Info("Downloading memory dump from storage")

	// 使用动态存储管理器下载文件
	tempPath, cleanup, err := e.storageManager.DownloadFileToTemp(ctx, fileInfo)
	if err != nil {
		return "", err
	}

	// 注意：这里不调用 cleanup()，因为调用者需要使用这个文件
	// cleanup 将在分析完成后由调用者处理
	_ = cleanup // 避免未使用变量警告

	return tempPath, nil
}

func (e *AnalysisEngine) downloadSymbols(ctx context.Context, symbolOption *models.SymbolOption) error {
	if e.storageManager == nil {
		e.logger.Warn("Storage manager not available, skipping symbol download")
		return nil
	}

	if symbolOption.FilePath == "" {
		e.logger.Info("No symbol path specified, skipping symbol download")
		return nil
	}

	e.logger.WithFields(map[string]interface{}{
		"symbol_path": symbolOption.FilePath,
		"bucket":      symbolOption.Bucket,
		"host":        symbolOption.OSS.Host,
	}).Info("Downloading symbol files")

	// 使用动态存储管理器下载符号文件
	_, err := e.storageManager.DownloadSymbols(ctx, symbolOption)
	if err != nil {
		e.logger.WithError(err).WithField("symbol_path", symbolOption.FilePath).Warn("Failed to download symbol file")
		return nil // 不阻止分析继续进行
	}

	e.logger.WithFields(map[string]interface{}{
		"symbol_path": symbolOption.FilePath,
		"bucket":      symbolOption.Bucket,
	}).Info("Symbol files downloaded successfully")

	return nil
}

func (e *AnalysisEngine) executePlugin(ctx context.Context, task *models.Task, pluginName, dumpPath, osType string, options *models.AnalysisOptions) error {
	// 获取插件选项
	pluginOptions := make(map[string]interface{})
	if options.ParseItems != nil {
		if pluginOpts, ok := options.ParseItems[pluginName].(map[string]interface{}); ok {
			pluginOptions = pluginOpts
		}
	}

	// 执行 Volatility 插件（带自动回退）
	result, err := e.volatilityExec.ExecuteWithFallback(ctx, pluginName, dumpPath, osType, pluginOptions)
	if err != nil {
		return fmt.Errorf("plugin execution failed: %w", err)
	}

	// 处理结果（字段映射、类型转换、文件处理）
	processedResult, err := e.resultProcessor.Process(result, osType)
	if err != nil {
		e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to process result")
		return fmt.Errorf("result processing failed: %w", err)
	}

	// 上传结果到存储
	if err := e.uploadResults(ctx, task, processedResult, pluginName, options); err != nil {
		e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to upload results")
		// 不返回错误，继续执行其他插件
	}

	// TODO: 发送 Kafka 通知
	e.logger.WithFields(map[string]interface{}{
		"plugin":            pluginName,
		"success":           result.Success,
		"raw_records":       len(result.RawData),
		"processed_records": len(processedResult.Data),
		"file_outputs":      len(processedResult.FileOutputs),
		"duration":          result.Duration,
	}).Info("Plugin execution and processing completed")

	return nil
}

func (e *AnalysisEngine) executeCustomPlugin(ctx context.Context, task *models.Task, pluginName, dumpPath, osType string, options *models.AnalysisOptions) error {
	// 获取自定义插件选项
	pluginOptions := make(map[string]interface{})
	if options.CustomParseItems != nil {
		if pluginOpts, ok := options.CustomParseItems[pluginName].(map[string]interface{}); ok {
			pluginOptions = pluginOpts
		}
	}

	// 执行自定义插件
	result, err := e.volatilityExec.ExecuteCustomPlugin(ctx, pluginName, dumpPath, osType, pluginOptions)
	if err != nil {
		return fmt.Errorf("custom plugin execution failed: %w", err)
	}

	// 处理结果（字段映射、类型转换、文件处理）
	processedResult, err := e.resultProcessor.Process(result, osType)
	if err != nil {
		e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to process custom plugin result")
		return fmt.Errorf("custom plugin result processing failed: %w", err)
	}

	// 上传结果到存储
	if err := e.uploadResults(ctx, task, processedResult, pluginName, options); err != nil {
		e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to upload custom plugin results")
		// 不返回错误，继续执行其他插件
	}

	// TODO: 发送 Kafka 通知
	e.logger.WithFields(map[string]interface{}{
		"plugin":            pluginName,
		"success":           result.Success,
		"raw_records":       len(result.RawData),
		"processed_records": len(processedResult.Data),
		"file_outputs":      len(processedResult.FileOutputs),
		"duration":          result.Duration,
	}).Info("Custom plugin execution and processing completed")

	return nil
}

// generateTaskID 生成任务 ID
func generateTaskID() string {
	return fmt.Sprintf("task_%d", time.Now().UnixNano())
}

// uploadResults 上传分析结果
func (e *AnalysisEngine) uploadResults(ctx context.Context, task *models.Task, result *models.ProcessedResult, pluginName string, options *models.AnalysisOptions) error {
	if e.storageManager == nil {
		e.logger.Warn("Storage manager not available, skipping result upload")
		return nil
	}

	// 生成结果文件路径
	resultPath := e.generateResultPath(task.FlowID, pluginName, options)

	// 序列化结果数据
	resultJSON, err := json.Marshal(result.Data)
	if err != nil {
		return fmt.Errorf("failed to marshal result data: %w", err)
	}

	// 上传结果文件
	// 生成文件名
	fileName := fmt.Sprintf("%s_%s.json", pluginName, task.FlowID)

	// 移除未使用的变量

	resultPath, err = e.storageManager.UploadFile(ctx, "",
		strings.NewReader(string(resultJSON)),
		int64(len(resultJSON)),
		&options.UploadOption,
		fileName)

	if err != nil {
		return fmt.Errorf("failed to upload result file: %w", err)
	}

	// 上传文件输出
	for _, fileOutput := range result.FileOutputs {
		if fileOutput.LocalPath != "" {
			err := e.uploadFileOutput(ctx, fileOutput, task.FlowID, pluginName)
			if err != nil {
				e.logger.WithError(err).WithFields(map[string]interface{}{
					"plugin":     pluginName,
					"local_path": fileOutput.LocalPath,
				}).Error("Failed to upload file output")
				// 继续处理其他文件
			}
		}
	}

	// 更新结果路径
	result.UploadPath = resultPath

	e.logger.WithFields(map[string]interface{}{
		"plugin":      pluginName,
		"result_path": resultPath,
		"file_count":  len(result.FileOutputs),
	}).Info("Results uploaded successfully")

	return nil
}

// uploadFileOutput 上传文件输出
func (e *AnalysisEngine) uploadFileOutput(ctx context.Context, fileOutput models.FileOutput, flowID, pluginName string) error {
	// 生成远程路径
	remotePath := filepath.Join("results", flowID, pluginName, "dump_file", filepath.Base(fileOutput.LocalPath))

	// 暂时跳过文件上传，因为动态管理器需要重新实现
	e.logger.WithFields(map[string]interface{}{
		"local_path":  fileOutput.LocalPath,
		"remote_path": remotePath,
	}).Info("File output upload skipped (not implemented for dynamic manager)")

	return nil
}

// generateResultPath 生成结果文件路径
func (e *AnalysisEngine) generateResultPath(flowID, pluginName string, options *models.AnalysisOptions) string {
	// 基础路径
	basePath := filepath.Join("results", flowID, pluginName)

	// 处理特殊插件的路径生成
	switch pluginName {
	case "Memmap":
		if options.ParseItems != nil {
			if memmapOptions, ok := options.ParseItems["windows.memmap.Memmap"].(map[string]interface{}); ok {
				if pid, ok := memmapOptions["pid"].([]interface{}); ok && len(pid) > 0 {
					return filepath.Join(basePath, fmt.Sprintf("%s_%v_res_file.json", pluginName, pid[0]))
				}
			}
		}
	case "VadInfo":
		if options.ParseItems != nil {
			if vadOptions, ok := options.ParseItems["windows.vadinfo.VadInfo"].(map[string]interface{}); ok {
				if pid, ok := vadOptions["pid"].([]interface{}); ok && len(pid) > 0 {
					return filepath.Join(basePath, fmt.Sprintf("%s_%v_res_file.json", pluginName, pid[0]))
				}
			}
		}
	case "vol2.linux_memmap":
		if options.ParseItems != nil {
			if memmapOptions, ok := options.ParseItems["vol2.linux_memmap"].(map[string]interface{}); ok {
				if pid, ok := memmapOptions["pid"].([]interface{}); ok && len(pid) > 0 {
					return filepath.Join(basePath, fmt.Sprintf("%s_%v_res_file.json", pluginName, pid[0]))
				}
			}
		}
	case "vol2.linux_moddump":
		if options.ParseItems != nil {
			if moddumpOptions, ok := options.ParseItems["vol2.linux_moddump"].(map[string]interface{}); ok {
				if offset, ok := moddumpOptions["offset"]; ok {
					return filepath.Join(basePath, fmt.Sprintf("%s_%v_res_file.json", pluginName, offset))
				}
			}
		}
	}

	// 默认路径
	return filepath.Join(basePath, fmt.Sprintf("%s_res_file.json", pluginName))
}

// getAnalysisID 从额外信息中获取分析 ID
func getAnalysisID(extra map[string]interface{}) string {
	if analysisID, ok := extra["analysis_id"].(string); ok {
		return analysisID
	}
	return ""
}

// GetTasksByStatus 根据状态获取任务列表
func (e *AnalysisEngine) GetTasksByStatus(status models.TaskStatus) []*models.Task {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	var tasks []*models.Task
	for _, task := range e.activeTasks {
		if task.Status == status {
			tasks = append(tasks, task)
		}
	}

	return tasks
}

// GetRunningTasks 获取正在运行的任务
func (e *AnalysisEngine) GetRunningTasks() []*models.Task {
	return e.GetTasksByStatus(models.TaskStatusRunning)
}

// GetPendingTasks 获取待处理的任务
func (e *AnalysisEngine) GetPendingTasks() []*models.Task {
	return e.GetTasksByStatus(models.TaskStatusPending)
}

// GetCompletedTasks 获取已完成的任务
func (e *AnalysisEngine) GetCompletedTasks() []*models.Task {
	return e.GetTasksByStatus(models.TaskStatusCompleted)
}

// GetFailedTasks 获取失败的任务
func (e *AnalysisEngine) GetFailedTasks() []*models.Task {
	return e.GetTasksByStatus(models.TaskStatusFailed)
}

// GetTaskCount 获取任务数量统计
func (e *AnalysisEngine) GetTaskCount() map[string]int {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	counts := map[string]int{
		"total":     len(e.activeTasks),
		"pending":   0,
		"running":   0,
		"completed": 0,
		"failed":    0,
		"cancelled": 0,
	}

	for _, task := range e.activeTasks {
		switch task.Status {
		case models.TaskStatusPending:
			counts["pending"]++
		case models.TaskStatusRunning:
			counts["running"]++
		case models.TaskStatusCompleted:
			counts["completed"]++
		case models.TaskStatusFailed:
			counts["failed"]++
		case models.TaskStatusCancelled:
			counts["cancelled"]++
		}
	}

	return counts
}

// CleanupCompletedTasks 清理已完成的任务（可选的清理策略）
func (e *AnalysisEngine) CleanupCompletedTasks(olderThan time.Duration) int {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()

	cutoffTime := time.Now().Add(-olderThan)
	cleanedCount := 0

	for flowID, task := range e.activeTasks {
		// 只清理已完成、失败或取消的任务
		if (task.Status == models.TaskStatusCompleted ||
			task.Status == models.TaskStatusFailed ||
			task.Status == models.TaskStatusCancelled) &&
			task.CompletedAt != nil &&
			task.CompletedAt.Before(cutoffTime) {
			delete(e.activeTasks, flowID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		e.logger.WithFields(map[string]interface{}{
			"cleaned_count": cleanedCount,
			"cutoff_time":   cutoffTime,
		}).Info("Cleaned up completed tasks")
	}

	return cleanedCount
}

// UpdateTaskProgress 更新任务进度
func (e *AnalysisEngine) UpdateTaskProgress(flowID string, progress float64, message string) error {
	e.taskMutex.Lock()
	defer e.taskMutex.Unlock()

	task, exists := e.activeTasks[flowID]
	if !exists {
		return fmt.Errorf("task not found for flow_id %s", flowID)
	}

	// 只有运行中的任务才能更新进度
	if task.Status != models.TaskStatusRunning {
		return fmt.Errorf("cannot update progress for task in status %s", task.Status)
	}

	task.Progress = progress
	if message != "" {
		if task.Metadata == nil {
			task.Metadata = make(map[string]interface{})
		}
		task.Metadata["last_message"] = message
		task.Metadata["last_update"] = time.Now().Unix()
	}

	// 发送进度更新通知
	if err := e.sendProgressUpdate(context.Background(), flowID, "", progress, message); err != nil {
		e.logger.WithError(err).WithField("flow_id", flowID).Error("Failed to send progress update")
	}

	return nil
}

// IsTaskActive 检查任务是否处于活跃状态
func (e *AnalysisEngine) IsTaskActive(flowID string) bool {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	task, exists := e.activeTasks[flowID]
	if !exists {
		return false
	}

	// 活跃状态包括待处理和运行中
	return task.Status == models.TaskStatusPending || task.Status == models.TaskStatusRunning
}

// GetTaskDuration 获取任务执行时长
func (e *AnalysisEngine) GetTaskDuration(flowID string) (time.Duration, error) {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	task, exists := e.activeTasks[flowID]
	if !exists {
		return 0, fmt.Errorf("task not found for flow_id %s", flowID)
	}

	if task.StartedAt == nil {
		return 0, nil // 任务还未开始
	}

	endTime := time.Now()
	if task.CompletedAt != nil {
		endTime = *task.CompletedAt
	}

	return endTime.Sub(*task.StartedAt), nil
}

// GetTaskMetrics 获取任务执行指标
func (e *AnalysisEngine) GetTaskMetrics() map[string]interface{} {
	e.taskMutex.RLock()
	defer e.taskMutex.RUnlock()

	metrics := map[string]interface{}{
		"total_tasks":      len(e.activeTasks),
		"task_counts":      e.GetTaskCount(),
		"average_duration": 0.0,
		"success_rate":     0.0,
	}

	var totalDuration time.Duration
	var completedTasks int
	var successfulTasks int

	for _, task := range e.activeTasks {
		if task.StartedAt != nil && task.CompletedAt != nil {
			duration := task.CompletedAt.Sub(*task.StartedAt)
			totalDuration += duration
			completedTasks++

			if task.Status == models.TaskStatusCompleted {
				successfulTasks++
			}
		}
	}

	if completedTasks > 0 {
		metrics["average_duration"] = totalDuration.Seconds() / float64(completedTasks)
		metrics["success_rate"] = float64(successfulTasks) / float64(completedTasks) * 100
	}

	metrics["completed_tasks"] = completedTasks
	metrics["successful_tasks"] = successfulTasks

	return metrics
}

// GetSchedulerStats 获取调度器统计信息
func (e *AnalysisEngine) GetSchedulerStats() map[string]interface{} {
	if e.scheduler == nil {
		return map[string]interface{}{
			"scheduler_enabled": false,
		}
	}

	stats := e.scheduler.GetSchedulerStats()
	stats["scheduler_enabled"] = true
	return stats
}

// Shutdown 优雅关闭分析引擎
func (e *AnalysisEngine) Shutdown(timeout time.Duration) error {
	e.logger.Info("Shutting down analysis engine")

	var shutdownErrors []error

	// 关闭调度器
	if e.scheduler != nil {
		if err := e.scheduler.Shutdown(timeout); err != nil {
			shutdownErrors = append(shutdownErrors, fmt.Errorf("scheduler shutdown error: %w", err))
		}
	}

	// 关闭消息管理器
	if e.messageManager != nil {
		if err := e.messageManager.Close(); err != nil {
			shutdownErrors = append(shutdownErrors, fmt.Errorf("message manager shutdown error: %w", err))
		}
	}

	// 关闭存储管理器
	if e.storageManager != nil {
		if err := e.storageManager.Close(); err != nil {
			shutdownErrors = append(shutdownErrors, fmt.Errorf("storage manager shutdown error: %w", err))
		}
	}

	// 取消所有活跃任务
	e.taskMutex.Lock()
	for _, task := range e.activeTasks {
		if task.Status == models.TaskStatusPending || task.Status == models.TaskStatusRunning {
			task.Status = models.TaskStatusCancelled
			task.Error = "System shutdown"
			cancelledAt := time.Now()
			task.CompletedAt = &cancelledAt
		}
	}
	e.taskMutex.Unlock()

	if len(shutdownErrors) > 0 {
		e.logger.WithField("errors", shutdownErrors).Error("Some components failed to shutdown gracefully")
		return fmt.Errorf("shutdown completed with %d errors", len(shutdownErrors))
	}

	e.logger.Info("Analysis engine shutdown completed")
	return nil
}

// SetMaxConcurrentTasks 动态设置最大并发任务数
func (e *AnalysisEngine) SetMaxConcurrentTasks(maxTasks int) error {
	if e.scheduler == nil {
		return fmt.Errorf("scheduler not initialized")
	}

	e.scheduler.SetMaxConcurrentTasks(maxTasks)
	e.logger.WithField("max_concurrent_tasks", maxTasks).Info("Max concurrent tasks updated")
	return nil
}

// GetQueueStatus 获取队列状态
func (e *AnalysisEngine) GetQueueStatus() map[string]interface{} {
	if e.scheduler == nil {
		return map[string]interface{}{
			"scheduler_enabled": false,
		}
	}

	return map[string]interface{}{
		"scheduler_enabled":    true,
		"queue_length":         e.scheduler.GetQueueLength(),
		"running_tasks":        e.scheduler.GetRunningTaskCount(),
		"max_concurrent_tasks": e.scheduler.GetMaxConcurrentTasks(),
		"available_slots":      e.scheduler.GetMaxConcurrentTasks() - e.scheduler.GetRunningTaskCount(),
	}
}

// sendTaskStatusNotification 发送任务状态通知
func (e *AnalysisEngine) sendTaskStatusNotification(ctx context.Context, task *models.Task) error {
	if e.messageManager == nil {
		return nil
	}

	return e.messageManager.PublishTaskStatus(ctx, task)
}

// sendErrorNotification 发送错误通知
func (e *AnalysisEngine) sendErrorNotification(ctx context.Context, flowID, plugin, errorMsg string) error {
	if e.messageManager == nil {
		return nil
	}

	return e.messageManager.PublishError(ctx, flowID, plugin, errorMsg)
}

// sendKafkaNotifications 发送 Kafka 通知
func (e *AnalysisEngine) sendKafkaNotifications(ctx context.Context, task *models.Task, result *models.ProcessedResult, pluginName string, duration time.Duration, options *models.AnalysisOptions) error {
	if e.messageManager == nil {
		e.logger.Warn("Message manager not available, skipping Kafka notifications")
		return nil
	}

	// 检查是否需要发送进度通知
	if options.ShowProgress {
		// 发送插件完成进度通知
		progressMsg := &models.ProgressMessage{
			FlowID:    task.FlowID,
			Plugin:    pluginName,
			Progress:  100.0, // 插件完成
			Status:    "completed",
			Message:   fmt.Sprintf("%s analysis completed in %v, %d records processed", pluginName, duration, len(result.Data)),
			Timestamp: time.Now().Unix(),
		}

		if err := e.messageManager.PublishProgress(ctx, progressMsg); err != nil {
			e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to publish progress message")
		}
	}

	// 发送结果通知
	dataMsg := &models.DataMessage{
		FlowID:     task.FlowID,
		Plugin:     pluginName,
		ResultPath: result.UploadPath,
		Status:     "completed",
		Timestamp:  time.Now().Unix(),
	}

	if err := e.messageManager.PublishResult(ctx, dataMsg); err != nil {
		e.logger.WithError(err).WithField("plugin", pluginName).Error("Failed to publish result message")
		return err
	}

	e.logger.WithFields(map[string]interface{}{
		"plugin":      pluginName,
		"flow_id":     task.FlowID,
		"result_path": result.UploadPath,
	}).Info("Kafka notifications sent successfully")

	return nil
}

// sendProgressUpdate 发送进度更新
func (e *AnalysisEngine) sendProgressUpdate(ctx context.Context, flowID, plugin string, progress float64, message string) error {
	if e.messageManager == nil {
		return nil
	}

	progressMsg := &models.ProgressMessage{
		FlowID:    flowID,
		Plugin:    plugin,
		Progress:  progress,
		Status:    "running",
		Message:   message,
		Timestamp: time.Now().Unix(),
	}

	return e.messageManager.PublishProgress(ctx, progressMsg)
}

// parseOSSConfig 解析 OSS 配置
func parseOSSConfig(ossMap map[string]interface{}) models.OSSConfig {
	config := models.OSSConfig{}

	if host, ok := ossMap["host"].(string); ok {
		config.Host = host
	}
	if ak, ok := ossMap["ak"].(string); ok {
		config.AK = ak
	}
	if sk, ok := ossMap["sk"].(string); ok {
		config.SK = sk
	}
	if ssl, ok := ossMap["ssl"].(bool); ok {
		config.SSL = ssl
	}

	return config
}
