package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"memory-go/internal/config"
	"memory-go/internal/models"
	"memory-go/pkg/logger"
)

// Manager 消息管理器
type Manager struct {
	producer MessageProducer
	config   *config.KafkaConfig
	logger   *logger.Logger
}

// NewManager 创建消息管理器
func NewManager(cfg *config.KafkaConfig, log *logger.Logger) (*Manager, error) {
	// 创建生产者配置
	producerConfig := &ProducerConfig{
		BootstrapServers: cfg.BootstrapServers,
		Acks:             "all",
		Retries:          3,
		BatchSize:        16384,
		LingerMs:         10,
		BufferMemory:     33554432, // 32MB
		Compression:      "snappy",
		IdempotentWrites: true,
		MaxInFlight:      5,
		RequestTimeout:   30 * time.Second,
	}

	// 创建 Kafka 生产者
	producer, err := NewKafkaProducer(producerConfig, log)
	if err != nil {
		return nil, fmt.Errorf("failed to create Kafka producer: %w", err)
	}

	return &Manager{
		producer: producer,
		config:   cfg,
		logger:   log,
	}, nil
}

// PublishProgress 发布进度消息
func (m *Manager) PublishProgress(ctx context.Context, progressMsg *models.ProgressMessage) error {
	topic := m.config.Topics["progress"]
	if topic == "" {
		return fmt.Errorf("progress topic not configured")
	}

	// 序列化消息
	data, err := json.Marshal(progressMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal progress message: %w", err)
	}

	message := &Message{
		ID:        fmt.Sprintf("progress_%s_%d", progressMsg.FlowID, time.Now().UnixNano()),
		Topic:     topic,
		Key:       progressMsg.FlowID,
		Value:     data,
		Timestamp: time.Now(),
		Headers: map[string]string{
			"message_type": "progress",
			"flow_id":      progressMsg.FlowID,
			"plugin":       progressMsg.Plugin,
		},
	}

	err = m.producer.Send(ctx, topic, message)
	if err != nil {
		m.logger.WithError(err).WithFields(map[string]interface{}{
			"topic":   topic,
			"flow_id": progressMsg.FlowID,
			"plugin":  progressMsg.Plugin,
		}).Error("Failed to publish progress message")
		return fmt.Errorf("failed to publish progress message: %w", err)
	}

	m.logger.WithFields(map[string]interface{}{
		"topic":    topic,
		"flow_id":  progressMsg.FlowID,
		"plugin":   progressMsg.Plugin,
		"progress": progressMsg.Progress,
	}).Debug("Progress message published")

	return nil
}

// PublishResult 发布结果消息
func (m *Manager) PublishResult(ctx context.Context, dataMsg *models.DataMessage) error {
	topic := m.config.Topics["data"]
	if topic == "" {
		return fmt.Errorf("data topic not configured")
	}

	// 序列化消息
	data, err := json.Marshal(dataMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal data message: %w", err)
	}

	message := &Message{
		ID:        fmt.Sprintf("data_%s_%d", dataMsg.FlowID, time.Now().UnixNano()),
		Topic:     topic,
		Key:       dataMsg.FlowID,
		Value:     data,
		Timestamp: time.Now(),
		Headers: map[string]string{
			"message_type": "data",
			"flow_id":      dataMsg.FlowID,
			"plugin":       dataMsg.Plugin,
			"status":       dataMsg.Status,
		},
	}

	err = m.producer.Send(ctx, topic, message)
	if err != nil {
		m.logger.WithError(err).WithFields(map[string]interface{}{
			"topic":       topic,
			"flow_id":     dataMsg.FlowID,
			"plugin":      dataMsg.Plugin,
			"result_path": dataMsg.ResultPath,
		}).Error("Failed to publish result message")
		return fmt.Errorf("failed to publish result message: %w", err)
	}

	m.logger.WithFields(map[string]interface{}{
		"topic":       topic,
		"flow_id":     dataMsg.FlowID,
		"plugin":      dataMsg.Plugin,
		"result_path": dataMsg.ResultPath,
		"status":      dataMsg.Status,
	}).Info("Result message published")

	return nil
}

// PublishError 发布错误消息
func (m *Manager) PublishError(ctx context.Context, flowID, plugin, errorMsg string) error {
	// 添加错误信息到消息中
	errorData := map[string]interface{}{
		"flow_id":   flowID,
		"plugin":    plugin,
		"status":    "error",
		"error":     errorMsg,
		"timestamp": time.Now().Unix(),
	}

	data, err := json.Marshal(errorData)
	if err != nil {
		return fmt.Errorf("failed to marshal error message: %w", err)
	}

	topic := m.config.Topics["data"]
	message := &Message{
		ID:        fmt.Sprintf("error_%s_%d", flowID, time.Now().UnixNano()),
		Topic:     topic,
		Key:       flowID,
		Value:     data,
		Timestamp: time.Now(),
		Headers: map[string]string{
			"message_type": "error",
			"flow_id":      flowID,
			"plugin":       plugin,
			"status":       "error",
		},
	}

	return m.producer.Send(ctx, topic, message)
}

// PublishTaskStatus 发布任务状态消息
func (m *Manager) PublishTaskStatus(ctx context.Context, task *models.Task) error {
	statusMsg := map[string]interface{}{
		"flow_id":      task.FlowID,
		"task_id":      task.ID,
		"status":       string(task.Status),
		"progress":     task.Progress,
		"created_at":   task.CreatedAt,
		"started_at":   task.StartedAt,
		"completed_at": task.CompletedAt,
		"error":        task.Error,
		"timestamp":    time.Now().Unix(),
	}

	data, err := json.Marshal(statusMsg)
	if err != nil {
		return fmt.Errorf("failed to marshal task status message: %w", err)
	}

	topic := m.config.Topics["progress"]
	message := &Message{
		ID:        fmt.Sprintf("status_%s_%d", task.FlowID, time.Now().UnixNano()),
		Topic:     topic,
		Key:       task.FlowID,
		Value:     data,
		Timestamp: time.Now(),
		Headers: map[string]string{
			"message_type": "task_status",
			"flow_id":      task.FlowID,
			"status":       string(task.Status),
		},
	}

	return m.producer.Send(ctx, topic, message)
}

// GetStats 获取统计信息
func (m *Manager) GetStats() map[string]interface{} {
	stats := map[string]interface{}{
		"producer_type": "kafka",
		"topics":        m.config.Topics,
		"servers":       m.config.BootstrapServers,
	}

	if kafkaProducer, ok := m.producer.(*KafkaProducer); ok {
		producerStats := kafkaProducer.GetStats()
		stats["sent"] = producerStats.Sent
		stats["failed"] = producerStats.Failed
	}

	return stats
}

// ValidateConnection 验证连接
func (m *Manager) ValidateConnection(ctx context.Context) error {
	// 发送测试消息
	testMsg := &Message{
		ID:        fmt.Sprintf("test_%d", time.Now().UnixNano()),
		Key:       "test",
		Value:     []byte(`{"test": "connection"}`),
		Timestamp: time.Now(),
		Headers: map[string]string{
			"message_type": "test",
		},
	}

	// 尝试发送到进度主题
	topic := m.config.Topics["progress"]
	if topic == "" {
		return fmt.Errorf("no progress topic configured")
	}

	err := m.producer.Send(ctx, topic, testMsg)
	if err != nil {
		return fmt.Errorf("connection validation failed: %w", err)
	}

	m.logger.Info("Message connection validated successfully")
	return nil
}

// Close 关闭消息管理器
func (m *Manager) Close() error {
	return m.producer.Close()
}
