# 内存转储文件存储说明

## 概述

从现在开始，`memory-go` 服务会将下载的内存分析镜像保存到当前工作目录下的 `dump_memory` 文件夹中，而不是使用临时文件。

## 主要变更

### 1. 新增持久化下载方法

在 `internal/storage/dynamic_manager.go` 中新增了 `DownloadMemoryDumpToPersistent` 方法：

```go
func (dm *DynamicManager) DownloadMemoryDumpToPersistent(ctx context.Context, fileInfo *models.FileInfo, targetDir string) (string, error)
```

### 2. 修改下载逻辑

在 `internal/engine/engine.go` 中修改了 `downloadMemoryDump` 方法：
- 不再使用临时文件
- 将文件保存到 `dump_memory` 目录
- 不再自动清理下载的文件

### 3. 文件命名规则

下载的文件会保持原始文件名，例如：
- 源文件：`mem-image/**********/win7.vmem`
- 本地文件：`dump_memory/win7.vmem`

如果无法从源路径提取文件名，会使用默认命名格式：
`memory_dump_{bucket}_{timestamp}.bin`

## 目录结构

```
memory-go/
├── dump_memory/           # 内存转储文件存储目录
│   ├── win7.vmem         # 下载的内存转储文件
│   └── ...               # 其他内存转储文件
├── logs/                 # 分析日志
├── internal/             # 源代码
└── ...
```

## 使用说明

### 1. 自动创建目录

当第一次下载内存转储文件时，`dump_memory` 目录会自动创建。

### 2. 文件管理

- **保留**：下载的内存转储文件会被保留在 `dump_memory` 目录中
- **清理**：如需清理，请手动删除 `dump_memory` 目录中的文件
- **版本控制**：`dump_memory` 目录已添加到 `.gitignore` 中，不会被提交到版本控制

### 3. 磁盘空间注意事项

内存转储文件通常很大（如示例中的 1GB），请确保有足够的磁盘空间：

```bash
# 检查磁盘空间
df -h .

# 查看 dump_memory 目录大小
du -sh dump_memory/

# 清理旧文件（如需要）
rm -rf dump_memory/*
```

## 配置示例

使用你提供的请求格式，文件会从 MinIO 服务器下载：

```json
{
  "file_info": {
    "download_option": {
      "type": "oss",
      "oss": {
        "host": "*************:9000",
        "ak": "minioadmin",
        "sk": "minioadmin",
        "ssl": false
      }
    },
    "oss": {
      "bucket": "mfp",
      "file": "mem-image/**********/win7.vmem"
    }
  }
}
```

下载后的文件路径：`dump_memory/win7.vmem`

## 日志记录

下载过程会记录详细日志：

```
2025-08-13T14:09:45.193+08:00 [INFO] MinIO provider initialized successfully
2025-08-13T14:09:45.199+08:00 [INFO] File stream opened successfully
2025-08-13T14:09:54.518+08:00 [INFO] Memory dump downloaded to persistent location
```

## 故障排除

### 1. 权限问题

确保应用程序有权限在当前目录创建 `dump_memory` 文件夹：

```bash
chmod 755 .
```

### 2. 磁盘空间不足

监控磁盘使用情况，及时清理不需要的文件：

```bash
# 监控磁盘使用
watch -n 5 'df -h . && echo "---" && du -sh dump_memory/'
```

### 3. 网络连接问题

确保能够连接到 MinIO 服务器：

```bash
# 测试连接
curl -I http://*************:9000
```

## 兼容性

这个改动向后兼容，不会影响现有的 API 接口和请求格式。
