# 内存转储文件和符号文件存储说明

## 概述

从现在开始，`memory-go` 服务会将下载的文件保存到当前工作目录下的持久化文件夹中，而不是使用临时文件：

- **内存转储文件**：保存到 `dump_memory/` 目录
- **符号文件**：保存到 `symbols/` 目录

## 主要变更

### 1. 新增持久化下载方法

在 `internal/storage/dynamic_manager.go` 中新增了持久化下载方法：

```go
// 内存转储文件
func (dm *DynamicManager) DownloadMemoryDumpToPersistent(ctx context.Context, fileInfo *models.FileInfo, targetDir string) (string, error)

// 符号文件
func (dm *DynamicManager) DownloadSymbolsToPersistent(ctx context.Context, symbolOption *models.SymbolOption, targetDir string) (string, error)
```

### 2. 修改下载逻辑

在 `internal/engine/engine.go` 中修改了下载方法：
- `downloadMemoryDump`：将内存转储文件保存到 `dump_memory` 目录
- `downloadSymbols`：将符号文件保存到 `symbols` 目录
- 不再使用临时文件
- 不再自动清理下载的文件

### 3. 文件命名规则

下载的文件会保持原始文件名，例如：

**内存转储文件：**
- 源文件：`mem-image/**********/win7.vmem`
- 本地文件：`dump_memory/win7.vmem`
- 默认命名：`memory_dump_{bucket}_{timestamp}.bin`

**符号文件：**
- 源文件：`symbols/1751944431/win7vmem.zip`
- 本地文件：`symbols/win7vmem.zip`
- 默认命名：`symbols_{bucket}_{timestamp}.zip`

## 目录结构

```
memory-go/
├── dump_memory/           # 内存转储文件存储目录
│   ├── win7.vmem         # 下载的内存转储文件 (1GB)
│   └── ...               # 其他内存转储文件
├── symbols/              # 符号文件存储目录
│   ├── win7vmem.zip      # 下载的符号文件 (317KB)
│   └── ...               # 其他符号文件
├── logs/                 # 分析日志
├── internal/             # 源代码
└── ...
```

## 使用说明

### 1. 自动创建目录

当第一次下载文件时，相应的目录会自动创建：
- `dump_memory/` - 内存转储文件目录
- `symbols/` - 符号文件目录

### 2. 文件管理

- **保留**：下载的文件会被保留在相应目录中
- **清理**：如需清理，请手动删除目录中的文件
- **版本控制**：`dump_memory/` 和 `symbols/` 目录已添加到 `.gitignore` 中，不会被提交到版本控制

### 3. 磁盘空间注意事项

内存转储文件通常很大，符号文件相对较小，请确保有足够的磁盘空间：

```bash
# 检查磁盘空间
df -h .

# 查看目录大小
du -sh dump_memory/ symbols/

# 清理旧文件（如需要）
rm -rf dump_memory/* symbols/*
```

## 配置示例

使用你提供的请求格式，文件会从 MinIO 服务器下载：

```json
{
  "file_info": {
    "download_option": {
      "type": "oss",
      "oss": {
        "host": "*************:9000",
        "ak": "minioadmin",
        "sk": "minioadmin",
        "ssl": false
      }
    },
    "oss": {
      "bucket": "mfp",
      "file": "mem-image/**********/win7.vmem"
    }
  }
}
```

下载后的文件路径：
- 内存转储文件：`dump_memory/win7.vmem`
- 符号文件：`symbols/win7vmem.zip`

## 日志记录

下载过程会记录详细日志：

**内存转储文件下载：**
```
2025-08-13T14:09:45.193+08:00 [INFO] MinIO provider initialized successfully
2025-08-13T14:09:45.199+08:00 [INFO] File stream opened successfully
2025-08-13T14:09:54.518+08:00 [INFO] Memory dump downloaded to persistent location
```

**符号文件下载：**
```
2025-08-13T14:14:27.030+08:00 [INFO] MinIO provider initialized successfully
2025-08-13T14:14:27.035+08:00 [INFO] File stream opened successfully
2025-08-13T14:14:27.041+08:00 [INFO] Symbol file downloaded to persistent location
```

## 故障排除

### 1. 权限问题

确保应用程序有权限在当前目录创建文件夹：

```bash
chmod 755 .
```

### 2. 磁盘空间不足

监控磁盘使用情况，及时清理不需要的文件：

```bash
# 监控磁盘使用
watch -n 5 'df -h . && echo "---" && du -sh dump_memory/ symbols/'
```

### 3. 网络连接问题

确保能够连接到 MinIO 服务器：

```bash
# 测试连接
curl -I http://*************:9000
```

## 兼容性

这个改动向后兼容，不会影响现有的 API 接口和请求格式。
