#!/bin/bash

# 测试内存分析日志功能

echo "Starting memory-go server..."
./bin/memory-go &
SERVER_PID=$!

# 等待服务器启动
sleep 3

echo "Testing health endpoint..."
curl -s http://localhost:8080/v1/health | jq .

echo -e "\nSending memory parse request..."

# 创建测试请求
REQUEST_JSON='{
  "file_info": {
    "type": "memory_dump",
    "sub_type": "windows",
    "attribute": {
      "os": "windows"
    },
    "oss": {
      "file": "test_memory.dmp",
      "bucket": "test-bucket"
    },
    "download_option": {}
  },
  "options": {
    "upload_option": {},
    "symbol": {},
    "kafka_option": {},
    "show_progress": true,
    "parse_items": {
      "pslist": {},
      "netstat": {}
    }
  },
  "extra": {
    "analysis_id": "test_analysis_12345",
    "flow_id": "test_flow_12345"
  }
}'

echo "Request JSON:"
echo "$REQUEST_JSON" | jq .

# 发送请求
RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "$REQUEST_JSON" \
  http://localhost:8080/v1/memory_parse)

echo -e "\nResponse:"
echo "$RESPONSE" | jq .

# 等待一段时间让分析开始
sleep 2

echo -e "\nChecking for log file..."
LOG_DIR="logs"
LOG_FILE="$LOG_DIR/test_analysis_12345.log"

if [ -f "$LOG_FILE" ]; then
    echo "✅ Log file created: $LOG_FILE"
    echo -e "\nLog content:"
    echo "----------------------------------------"
    cat "$LOG_FILE"
    echo "----------------------------------------"
else
    echo "❌ Log file not found: $LOG_FILE"
    echo "Checking log directory..."
    ls -la "$LOG_DIR" 2>/dev/null || echo "Log directory does not exist"
fi

echo -e "\nTesting log API endpoint..."
curl -s "http://localhost:8080/v1/memory_parse/log/test_analysis_12345" | jq .

# 清理
echo -e "\nStopping server..."
kill $SERVER_PID
wait $SERVER_PID 2>/dev/null

echo "Test completed."