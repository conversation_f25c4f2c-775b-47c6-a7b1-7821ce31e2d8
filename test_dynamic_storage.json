{"file_info": {"type": "file", "sub_type": "memory_dump", "attribute": {"os": {"type": "Windows"}}, "download_option": {"type": "oss", "oss": {"host": "*************:9000", "ak": "minioadmin", "sk": "minioadmin", "ssl": false}}, "oss": {"bucket": "mfp", "file": "mem-image/1751944328/win7.vmem"}}, "options": {"upload_option": {"type": "oss", "oss": {"host": "*************:9000", "ak": "minioadmin", "sk": "minioadmin", "ssl": false}, "bucket": "mfp", "path": "/analysis-result/test_dynamic_storage"}, "symbol": {"type": "oss", "oss": {"host": "*************:9000", "ak": "minioadmin", "sk": "minioadmin", "ssl": false}, "bucket": "mfp", "file_path": "symbols/1751944431/win7vmem.zip"}, "kafka_option": {"topic": ["mfp_result"]}, "parse_items": {"windows.pslist.PsList": {"is_parse": true}}, "show_progress": true}, "extra": {"analysis_id": "test_dynamic_storage_123", "task_id": "Default-task", "image_id": "test-image-123"}}