package main

import (
	"fmt"

	"memory-go/internal/config"
)

func main() {
	fmt.Println("=== 测试 Kafka 配置 ===")

	// 加载配置文件
	cfg, err := config.Load("configs/memory_analyzer.yaml")
	if err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		return
	}

	fmt.Printf("Kafka 服务器: %v\n", cfg.Kafka.BootstrapServers)
	fmt.Println("主题配置:")
	for key, topic := range cfg.Kafka.Topics {
		fmt.Printf("  %s: %s\n", key, topic)
	}

	// 验证所有主题都指向 mfp_result
	allTopicsCorrect := true
	expectedTopic := "mfp_result"

	for key, topic := range cfg.Kafka.Topics {
		if topic != expectedTopic {
			fmt.Printf("❌ 主题 %s 配置错误: 期望 %s，实际 %s\n", key, expectedTopic, topic)
			allTopicsCorrect = false
		}
	}

	if allTopicsCorrect {
		fmt.Printf("✅ 所有消息类型都正确配置为发送到主题: %s\n", expectedTopic)
	}

	fmt.Println("\n=== 配置验证完成 ===")
}
